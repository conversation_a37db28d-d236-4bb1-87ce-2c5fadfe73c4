version: "3.8"

services:
  auth-service:
    image: mvs-vr-v2-auth-service-fixed:20250530-135418
    restart: always
    ports:
      - "3005:3005"

  api-gateway:
    image: api-gateway-20250530-135418
    restart: always
    ports:
      - "4000:4000"
    depends_on:
      - auth-service

  asset-service:
    image: asset-service-20250530-135418
    restart: always
    ports:
      - "5000:5000"

  analytics-service:
    image: analytics-service-20250530-135418
    restart: always
    ports:
      - "6000:6000"

  blueprint-service:
    image: blueprint-service-20250530-135418
    restart: always
    ports:
      - "7000:7000"

  llm-service:
    image: llm-service-20250530-135418
    restart: always
    ports:
      - "8000:8000"

  monitoring-service:
    image: monitoring-service-20250530-135418
    restart: always
    ports:
      - "9000:9000"

  directus:
    image: directus-20250530-135418
    restart: always
    ports:
      - "8055:8055"
